<?php
namespace app\manage\controller\manage;

use think\App;
use think\Request;
use think\facade\View;
use think\facade\Session;
use app\common\controller\BaseController;
use app\common\model\Announcement as AnnouncementModel;
use app\manage\service\AnnouncementPushService;
use app\manage\model\UserAdmin;

class Announcement extends BaseController
{
    protected $admin;

    public function __construct(App $app, Request $request) {
        parent::__construct($app, $request);

        // 检查管理员登录状态
        $adminId = $_SESSION['admin'] ?? null;
        if($adminId == 0 || $adminId == null) {
            header('Location: /manage/login');
            exit;
        }

        // 获取完整的管理员信息
        try {
            $adminInfo = UserAdmin::find($adminId);
            if ($adminInfo) {
                $this->admin = [
                    'uid' => $adminInfo['uid'],
                    'username' => $adminInfo['username']
                ];
            } else {
                // 如果找不到管理员信息，使用默认值
                $this->admin = [
                    'uid' => $adminId,
                    'username' => 'admin'
                ];
            }
        } catch (\Exception $e) {
            // 数据库查询失败时使用默认值
            $this->admin = [
                'uid' => $adminId,
                'username' => 'admin'
            ];
        }
    }

    /**
     * 公告列表页面
     */
    public function index() {
        if($this->request->isPost()) {
            // 处理POST请求
            $action = $this->request->param('action', '');

            switch($action) {
                case 'delete':
                    return $this->deleteAnnouncement();
                case 'publish':
                    return $this->publishAnnouncement();
                case 'offline':
                    return $this->offlineAnnouncement();
                case 'batch':
                    return $this->batchOperation();
                case 'detail':
                    return $this->getDetail();
                default:
                    return json(['code' => 0, 'msg' => '未知操作']);
            }
        }

        $page = $this->request->param('page', 1);
        $filters = [
            'status' => $this->request->param('status', ''),
            'type' => $this->request->param('type', ''),
            'keyword' => $this->request->param('keyword', '')
        ];

        $announcements = AnnouncementModel::getAdminList($page, 20, $filters);
        $stats = AnnouncementModel::getStats();

        return View::fetch('announcement/index', [
            'announcements' => $announcements,
            'stats' => $stats,
            'filters' => $filters
        ]);
    }

    /**
     * 添加公告页面
     */
    public function add() {
        if ($this->request->isPost()) {
            try {
                $data = $this->request->post();
                
                // 验证数据
                $this->validateAnnouncementData($data);
                
                // 创建公告
                $announcement = AnnouncementModel::createAnnouncement($data, $this->admin);

                if ($announcement && is_object($announcement)) {
                    // 如果创建的公告是已发布状态，发送实时推送通知
                    if (isset($data['status']) && $data['status'] == 1) {
                        AnnouncementPushService::publishAnnouncementUpdate($announcement->toArray());
                    }

                    return json(['code' => 1, 'msg' => '公告创建成功', 'url' => '/manage/admin/announcement']);
                } else {
                    return json(['code' => 0, 'msg' => '公告创建失败']);
                }

            } catch (\Exception $e) {
                return json(['code' => 0, 'msg' => '创建失败：' . $e->getMessage()]);
            }
        }
        
        return View::fetch('announcement/add');
    }

    /**
     * 编辑公告页面
     */
    public function edit() {
        $id = $this->request->param('id');
        
        if (!$id) {
            return json(['code' => 0, 'msg' => '参数错误']);
        }

        $announcement = AnnouncementModel::find($id);
        if (!$announcement) {
            return json(['code' => 0, 'msg' => '公告不存在']);
        }
        
        if ($this->request->isPost()) {
            try {
                $data = $this->request->post();
                
                // 验证数据
                $this->validateAnnouncementData($data);
                
                // 更新公告
                $result = $announcement->updateAnnouncement($data, $this->admin);

                if ($result && is_object($result)) {
                    // 如果更新的公告是已发布状态，发送实时推送通知
                    if (isset($data['status']) && $data['status'] == 1) {
                        AnnouncementPushService::publishAnnouncementUpdate($result->toArray());
                    }

                    return json(['code' => 1, 'msg' => '公告更新成功', 'url' => '/manage/admin/announcement']);
                } else {
                    return json(['code' => 0, 'msg' => '公告更新失败']);
                }

            } catch (\Exception $e) {
                return json(['code' => 0, 'msg' => '更新失败：' . $e->getMessage()]);
            }
        }
        
        return View::fetch('announcement/edit', [
            'announcement' => $announcement
        ]);
    }

    /**
     * 删除公告（私有方法）
     */
    private function deleteAnnouncement() {
        $id = $this->request->param('id');

        if (!$id) {
            return json(['code' => 0, 'msg' => '参数错误']);
        }

        $announcement = AnnouncementModel::find($id);
        if (!$announcement) {
            return json(['code' => 0, 'msg' => '公告不存在']);
        }

        try {
            $result = $announcement->deleteAnnouncement();

            if ($result) {
                return json(['code' => 1, 'msg' => '公告删除成功']);
            } else {
                return json(['code' => 0, 'msg' => '公告删除失败']);
            }

        } catch (\Exception $e) {
            return json(['code' => 0, 'msg' => '删除失败：' . $e->getMessage()]);
        }
    }

    /**
     * 发布公告（私有方法）
     */
    private function publishAnnouncement() {
        $id = $this->request->param('id');

        if (!$id) {
            return json(['code' => 0, 'msg' => '参数错误']);
        }

        $announcement = AnnouncementModel::find($id);
        if (!$announcement) {
            return json(['code' => 0, 'msg' => '公告不存在']);
        }

        try {
            $result = $announcement->publish();

            if ($result) {
                // 发布成功后，发送实时推送通知
                AnnouncementPushService::publishAnnouncementUpdate($announcement->toArray());

                return json(['code' => 1, 'msg' => '公告发布成功']);
            } else {
                return json(['code' => 0, 'msg' => '公告发布失败']);
            }

        } catch (\Exception $e) {
            return json(['code' => 0, 'msg' => '发布失败：' . $e->getMessage()]);
        }
    }

    /**
     * 下线公告（私有方法）
     */
    private function offlineAnnouncement() {
        $id = $this->request->param('id');

        if (!$id) {
            return json(['code' => 0, 'msg' => '参数错误']);
        }

        $announcement = AnnouncementModel::find($id);
        if (!$announcement) {
            return json(['code' => 0, 'msg' => '公告不存在']);
        }

        try {
            $result = $announcement->offline();

            if ($result) {
                return json(['code' => 1, 'msg' => '公告已下线']);
            } else {
                return json(['code' => 0, 'msg' => '下线失败']);
            }

        } catch (\Exception $e) {
            return json(['code' => 0, 'msg' => '下线失败：' . $e->getMessage()]);
        }
    }

    /**
     * 批量操作（私有方法）
     */
    private function batchOperation() {
        $action = $this->request->param('action');
        $ids = $this->request->param('ids');
        
        if (!$action || !$ids) {
            return json(['code' => 0, 'msg' => '参数错误']);
        }
        
        $ids = is_array($ids) ? $ids : explode(',', $ids);
        
        try {
            $count = 0;
            foreach ($ids as $id) {
                $announcement = AnnouncementModel::find($id);
                if ($announcement) {
                    switch ($action) {
                        case 'publish':
                            $announcement->publish();
                            $count++;
                            break;
                        case 'offline':
                            $announcement->offline();
                            $count++;
                            break;
                        case 'delete':
                            $announcement->deleteAnnouncement();
                            $count++;
                            break;
                    }
                }
            }
            
            return json(['code' => 1, 'msg' => "成功处理 {$count} 条公告"]);

        } catch (\Exception $e) {
            return json(['code' => 0, 'msg' => '批量操作失败：' . $e->getMessage()]);
        }
    }

    /**
     * 获取公告详情（私有方法）
     */
    private function getDetail() {
        $id = $this->request->param('id');
        
        if (!$id) {
            return json(['code' => 1, 'msg' => '参数错误']);
        }
        
        $announcement = AnnouncementModel::find($id);
        if (!$announcement) {
            return json(['code' => 1, 'msg' => '公告不存在']);
        }
        
        return json([
            'code' => 0,
            'data' => $announcement->toArray()
        ]);
    }

    /**
     * 验证公告数据
     */
    private function validateAnnouncementData($data) {
        if (empty($data['title'])) {
            throw new \Exception('公告标题不能为空');
        }
        
        if (empty($data['content'])) {
            throw new \Exception('公告内容不能为空');
        }
        
        if (strlen($data['title']) > 200) {
            throw new \Exception('公告标题不能超过200个字符');
        }
        
        if (!in_array($data['type'], [1, 2, 3])) {
            throw new \Exception('公告类型无效');
        }
        
        if (!in_array($data['status'], [0, 1, 2])) {
            throw new \Exception('公告状态无效');
        }
        
        if (!in_array($data['target_client'], ['all', 'desktop', 'mobile'])) {
            throw new \Exception('目标客户端类型无效');
        }
        
        // 验证时间格式
        if (!empty($data['start_time']) && !strtotime($data['start_time'])) {
            throw new \Exception('开始时间格式无效');
        }
        
        if (!empty($data['end_time']) && !strtotime($data['end_time'])) {
            throw new \Exception('结束时间格式无效');
        }
        
        // 验证时间逻辑
        if (!empty($data['start_time']) && !empty($data['end_time'])) {
            if (strtotime($data['start_time']) >= strtotime($data['end_time'])) {
                throw new \Exception('结束时间必须大于开始时间');
            }
        }
    }
}
