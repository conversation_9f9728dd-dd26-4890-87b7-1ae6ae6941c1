<?php /*a:2:{s:101:"E:\Build\C++\bnspatch\src\BnsPlugin\server\tools.bnszs.com_tp6\app\manage\view\admin\risk_events.html";i:1752135343;s:99:"E:\Build\C++\bnspatch\src\BnsPlugin\server\tools.bnszs.com_tp6\app\manage\view\manage\template.html";i:1751973426;}*/ ?>
<!DOCTYPE html>
<html>
<head>
  <!-- 项目构建：兔子、0x1ng、Xylia  | 项目创建:2020-06-01  | 项目更新:2025-02-05 -->
  <meta charset="utf-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="description" content="剑灵小助手管理系统。">
  <meta name="keywords" content="剑灵骗子,剑灵骗子大全,游戏骗子,剑灵骗子数据库,小助手骗子数据库,小助手提交骗子,小助手自定义,装备查询优化,装备查询自定义,小助手装备查询,剑灵装备查询,剑灵小助手">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <meta name="renderer" content="webkit">
  <meta name="apple-mobile-web-app-title" content="Amaze UI" />
  <meta http-equiv="Cache-Control" content="no-siteapp" />

  <title>剑灵小助手管理系统</title>
  <link rel="icon shortcut" href="/favicon.ico" type="image/x-icon">
  <link rel="stylesheet" href="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/bootstrap/5.1.3/css/bootstrap.css">
  <link rel="stylesheet" href="/css/manage.css?version=2025021504"/>
  <link rel="stylesheet" href="/css/admin.css?version=2021021110">
  <link rel="stylesheet" href="/css/tally.css?version=2021021112">

  <script src="https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/jquery/3.6.0/jquery.min.js"></script>
  <script src="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/bootstrap/5.1.3/js/bootstrap.min.js"></script>
  <script src="https://static-1251192097.cos.ap-shanghai.myqcloud.com/web_html/assets/layer/layer.js"></script>
  <script src="/js/amazeui.min.js"></script>
  <script src="/js/manage.js"></script>

  <script>
  // 移动设备侧边栏控制函数 - 全局定义
  function toggleMobileSidebar() {
    console.log('toggleMobileSidebar called');
    var sidebar = document.getElementById('admin-offcanvas');
    var overlay = document.querySelector('.mobile-sidebar-overlay');

    console.log('sidebar:', sidebar);
    console.log('overlay:', overlay);

    if (!sidebar) {
      console.error('Sidebar not found!');
      return;
    }

    if (sidebar.classList.contains('mobile-show')) {
      console.log('Hiding sidebar');
      hideMobileSidebar();
    } else {
      console.log('Showing sidebar');
      showMobileSidebar();
    }
  }

  function showMobileSidebar() {
    console.log('showMobileSidebar called');
    var sidebar = document.getElementById('admin-offcanvas');
    var overlay = document.querySelector('.mobile-sidebar-overlay');

    if (sidebar) {
      sidebar.classList.add('mobile-show');
      console.log('Added mobile-show class to sidebar');
    }

    if (overlay) {
      overlay.classList.add('show');
      console.log('Added show class to overlay');
    }
  }

  function hideMobileSidebar() {
    console.log('hideMobileSidebar called');
    var sidebar = document.getElementById('admin-offcanvas');
    var overlay = document.querySelector('.mobile-sidebar-overlay');

    if (sidebar) {
      sidebar.classList.remove('mobile-show');
      console.log('Removed mobile-show class from sidebar');
    }

    if (overlay) {
      overlay.classList.remove('show');
      console.log('Removed show class from overlay');
    }
  }
  </script>

  <style type="text/css">
  	.ripple {
		position: relative;
		overflow: hidden;
	}
			
	.ripple:after {
		content: "";
		display: block;
		position: absolute;
		width: 100%;
		height: 100%;
		top: 0;
		left: 0;
		pointer-events: none;
		background-image: radial-gradient(circle, #666 10%, transparent 10.01%);
		background-repeat: no-repeat;
		background-position: 50%;
		transform: scale(10, 10);
		opacity: 0;
		transition: transform .3s, opacity .5s;
	}
			
	.ripple:active:after {
		transform: scale(0, 0);
		opacity: .3;
		transition: 0s;
	}

	.btn-sign{
		border-width:0px;
		width: 80px;
		height: 80px;
	}
			
	.option {
		width: 200px;
		height: 40px;
		border: 1px solid #cccccc;
		position: relative;
	}

	.option select {
		border: none;
		outline: none;
		width: 100%;
		height: 40px;
		line-height: 40px;
		appearance: none;
		-webkit-appearance: none;
		-moz-appearance: none;
		padding-left: 20px;
	}

	.option:after {
		content: "";
		width: 14px;
		height: 8px;
		background: url(/assets/arrow-down.png) no-repeat center;
		position: absolute;
		right: 20px;
		top: 41%;
		pointer-events: none;
	}

	/* 统计卡片样式 */
	.stats-card {
		background: #f8f9fa;
		border: 1px solid #e9ecef;
		border-radius: 8px;
		padding: 20px;
		text-align: center;
		margin-bottom: 15px;
		transition: all 0.3s ease;
	}

	.stats-card:hover {
		background: #e9ecef;
		transform: translateY(-2px);
		box-shadow: 0 4px 8px rgba(0,0,0,0.1);
	}

	.stats-number {
		font-size: 2.5em;
		font-weight: bold;
		color: #007bff;
		margin-bottom: 5px;
	}

	.stats-label {
		color: #6c757d;
		font-size: 0.9em;
		text-transform: uppercase;
		letter-spacing: 0.5px;
	}

	/* 统计卡片链接样式 */
	.stats-card-link {
		display: block;
		text-decoration: none;
		color: inherit;
	}

	.stats-card-link:hover {
		text-decoration: none;
		color: inherit;
	}

	/* 状态颜色 */
	.status-normal { color: #28a745; }
	.status-banned { color: #dc3545; }
	.status-premium { color: #ffc107; }
	.status-online { color: #17a2b8; }

	/* 移动设备侧边栏优化 */
	@media only screen and (max-width: 640px) {
		#admin-offcanvas {
			position: fixed !important;
			left: -260px !important;
			top: 51px !important;
			bottom: 0 !important;
			z-index: 1600 !important;
			transition: left 0.3s ease !important;
			background: #fff !important;
			box-shadow: 2px 0 5px rgba(0,0,0,0.1) !important;
			width: 260px !important;
			height: calc(100vh - 51px) !important;
			overflow-y: auto !important;
			border: 1px solid #ddd !important;
		}

		#admin-offcanvas.mobile-show {
			left: 0 !important;
		}

		/* 确保侧边栏内容可见 */
		#admin-offcanvas .am-offcanvas-bar {
			position: static !important;
			transform: none !important;
			width: 100% !important;
			height: 100% !important;
			background: #fff !important;
			padding: 10px !important;
		}

		/* 确保侧边栏菜单项可见 */
		#admin-offcanvas .admin-sidebar-list {
			background: #fff !important;
			margin: 0 !important;
			padding: 0 !important;
		}

		#admin-offcanvas .admin-sidebar-list li {
			background: #fff !important;
			border-bottom: 1px solid #eee !important;
		}

		#admin-offcanvas .admin-sidebar-list li a {
			color: #333 !important;
			padding: 12px 15px !important;
			display: block !important;
			text-decoration: none !important;
		}

		#admin-offcanvas .admin-sidebar-list li a:hover {
			background: #f5f5f5 !important;
			color: #1E9FFF !important;
		}

		.admin-content {
			margin-left: 0 !important;
		}

		.mobile-sidebar-toggle {
			background: #1E9FFF !important;
			border-color: #1E9FFF !important;
			color: white !important;
			border: none !important;
			padding: 6px 12px !important;
			border-radius: 3px !important;
		}

		.mobile-sidebar-toggle:hover {
			background: #0e7ce8 !important;
			border-color: #0e7ce8 !important;
		}

		/* 确保移动端菜单按钮在右侧正确显示 */
		.am-topbar-right .am-show-sm-only {
			float: right;
		}

		.am-topbar-right .mobile-sidebar-toggle {
			margin: 8px 10px 8px 0;
		}

		/* 遮罩层 */
		.mobile-sidebar-overlay {
			position: fixed;
			top: 51px;
			left: 0;
			right: 0;
			bottom: 0;
			background: rgba(0,0,0,0.5);
			z-index: 1500;
			display: none;
		}

		.mobile-sidebar-overlay.show {
			display: block;
		}
	}
  </style>
</head>
<body>
	<header class="am-topbar am-topbar-inverse admin-header">
	  <div class="am-topbar-brand">
		<strong>剑灵小助手管理系统</strong> <small> 堕络</small>
	  </div>

	  <div class="am-collapse am-topbar-collapse" id="topbar-collapse">
		<ul class="am-nav am-nav-pills am-topbar-nav am-topbar-right admin-header-list">
			<?php if(session("admin")): ?>
			<li class="am-dropdown" data-am-dropdown>
				<a class="am-dropdown-toggle" data-am-dropdown-toggle href="javascript:void(0);" onclick="SwitchPanel('#admin-dropdown-content')">
				  <span class="am-icon-admin"></span> 管理员 <span class="am-icon-caret-down"></span>
				</a>
				<ul class="am-dropdown-content" id="admin-dropdown-content">
				  <li><a href="javascript:void(0);" onclick="window.location.href='/admin/userinfo.php?id=1'"><span class="am-icon-admin"></span> 资料</a></li>
				  <li><a href="javascript:void(0);" onclick="logout()"><span class="am-icon-power-off"></span> 退出</a></li>
				</ul>
			  </li>
			<?php endif; ?>
			<li class="am-hide-sm-only">
				<?php if(session("user")): ?>
				<a href="/manage/center" id="user">
					<span class="am-icon-users"></span>
					<span class="admin-fullText">个人中心</span>
				</a>
				<?php else: ?>
				<a href="/manage/login" id="user">
					<span class="am-icon-users"></span>
					<span class="admin-fullText">登录账号</span>
				</a>
				<?php endif; ?>
			</li>
			<!-- 移动设备侧边栏切换按钮 - 放在最右边 -->
			<li class="am-show-sm-only">
				<button class="am-topbar-btn am-btn am-btn-sm am-btn-primary mobile-sidebar-toggle" onclick="toggleMobileSidebar()">
					<span class="am-sr-only">菜单</span> <span class="am-icon-navicon"></span>
				</button>
			</li>
		</ul>
	  </div>
	</header>

	<div class="am-cf admin-main">
	  <!-- sidebar start -->
	  <div class="admin-sidebar am-offcanvas" id="admin-offcanvas">
		<div class="am-offcanvas-bar admin-offcanvas-bar">
		  <ul class="am-list admin-sidebar-list">
			<li><a href="/manage"><span class="am-icon-home"></span> 系统首页</a></li>
			<!-- <li><a href="/manage/liars"><span class="am-icon-th"></span> 骗子列表 <span class="am-badge am-badge-secondary am-margin-right am-fr"></span></a></li> -->
			<!-- <li>
			  <a href="/manage/liarpost"><span class="am-icon-pencil-square-o"></span> <?php if(session('admin')) echo('提交骗子<span class="am-badge am-badge-secondary am-margin-right am-fr">管理</span>'); 		
				  else echo('举报骗子'); 
			  ?></a>
			</li> -->
			<?php if(isset($_SESSION['admin']) && $_SESSION['admin']): 			try {
				$adminMenuItems = app\manage\model\UserAdmin::GetItems();
				if (!empty($adminMenuItems)) {
					foreach ($adminMenuItems as $item) {?>
						<li><a href="<?php echo $item['url']; ?>"><span class="<?php echo $item['icon']; ?>"></span> <?php echo $item['itemName']; ?></a></li>
					<?php }
				} else { ?>
					<li><a href="#"><span class="am-icon-warning"></span> 暂无管理菜单</a></li>
				<?php }
			} catch (Exception $e) { ?>
				<li><a href="#"><span class="am-icon-exclamation-triangle"></span> 菜单加载失败</a></li>
			<?php } ?>
			<?php endif; ?>
			<li><a href="/manage/profile"><span class="am-icon-gift"></span> 自定义资料</a></li>
			<!-- <li><a href="/manage/help"><span class="am-icon-map-signs"></span> 防骗指南</a></li> -->
			<!-- <li><a href="/manage/choose"><span class="am-icon-paint-brush"></span> 安全测试</a></li> -->
			<?php if(session('user')){ ?> <li><a href="/manage/logout"><span class="am-icon-sign-out"></span> 注销</a></li> <?php } ?>
		  </ul>
		  
		  <div class="am-panel am-panel-default admin-sidebar-panel">
			<div class="am-panel-bd">
			  <p><span class="am-icon-bookmark"></span> 公告</p>
			  <div class="line">
                    <span>萌新必看：</span>
                    <p><a class="home" target="_blank" href="https://docs.qq.com/doc/p/9839e342f1dd89219e2e2980a9a803a42b9d94cf">2.0.1 使用指南</a></p>
			        <p><a class="home" target="_blank" href="https://docs.qq.com/doc/p/7ea1c83b63c8b59b7472c999d15156c4fb843d31">3.0 新版使用指南</a></p>
			        
               </div>
			  
			  <div class="line">
                    <span>剑灵小助手[3.0]：</span>
                    <p>
                    <a class="home" target="_blank" href="https://pan.quark.cn/s/94255b808597">夸克网盘</a>
                    <a class="home" target="_blank" href="https://pan.baidu.com/s/1qcuS-obYbHKBQAuuH6_Bhw?pwd=5210">百度网盘</a>
                    </p>
               </div>
               <div class="line">
                    <span>剑灵小助手[2.0.1]：</span>
                    <p>
                    <a class="home" target="_blank" href="https://pan.quark.cn/s/2ad567e2b816#/list/share">夸克网盘</a>
                    <a class="home" target="_blank" href="https://pan.baidu.com/s/1irL97YxJfR1-UWxkxqXHIQ?pwd=5210">百度网盘</a>
                    <a class="home" target="_blank" href="https://www.lanzoul.com/iUyTU1irepef">蓝奏云</a>
                    </p>
                </div>
			  
			  <div class="texts" style="margin-top: 20px;">
                    <p><span>助手①群：<code>548810086</code></span></p>
                    <p><span>助手②群：<code>563768233</code></span></p>
                    <p><span>助手③群：<code>618986361</code></span></p>
                </div>
			</div>
		  </div>
		</div>
	  </div>

	  <!-- 移动设备侧边栏遮罩层 -->
	  <div class="mobile-sidebar-overlay" onclick="hideMobileSidebar()"></div>

	  

<div class="admin-content">
	<div class="admin-content-body">
		<div class="am-cf am-padding am-padding-bottom-0">
			<div class="am-fl am-cf">
				<strong class="am-text-primary am-text-lg">风险事件管理</strong> / <small>Risk Events Management</small>
			</div>
		</div>
		<hr>
		<!-- 筛选栏 -->
		<div class="filter-bar">
			<form class="am-form am-form-inline" method="get">
				<div class="am-form-group">
					<label>状态:</label>
					<select name="status" class="am-form-field">
						<option value="">全部</option>
						<option value="pending" <?php if($status == 'pending'): ?>selected<?php endif; ?>>待处理</option>
						<option value="processed" <?php if($status == 'processed'): ?>selected<?php endif; ?>>已处理</option>
						<option value="ignored" <?php if($status == 'ignored'): ?>selected<?php endif; ?>>已忽略</option>
					</select>
				</div>
				<div class="am-form-group">
					<label>严重程度:</label>
					<select name="severity" class="am-form-field">
						<option value="">全部</option>
						<option value="high" <?php if($severity == 'high'): ?>selected<?php endif; ?>>高风险</option>
						<option value="medium" <?php if($severity == 'medium'): ?>selected<?php endif; ?>>中风险</option>
						<option value="low" <?php if($severity == 'low'): ?>selected<?php endif; ?>>低风险</option>
					</select>
				</div>
				<button type="submit" class="am-btn am-btn-primary">筛选</button>
				<a href="/manage/admin/risk/events" class="am-btn am-btn-default">重置</a>
			</form>
		</div>
		<!-- 事件列表 -->
		<div class="am-panel am-panel-default">
			<div class="am-panel-hd"> 风险事件列表 <div class="am-fr">
					<a href="/manage/admin/risk/dashboard" class="am-btn am-btn-xs am-btn-secondary">
						<i class="am-icon-dashboard"></i> 返回仪表板 </a>
				</div>
			</div>
			<div class="am-panel-bd"> <?php if($events->items()): ?> <div class="am-scrollable-horizontal">
					<table class="am-table am-table-striped am-table-hover">
						<thead>
							<tr>
								<th>ID</th>
								<th>事件类型</th>
								<th>严重程度</th>
								<th>状态</th>
								<th>涉及QQ号</th>
								<th>设备/IP</th>
								<th>触发次数</th>
								<th>创建时间</th> <?php if($can_manage): ?> <th>操作</th> <?php endif; ?>
							</tr>
						</thead>
						<tbody> <?php if(is_array($events) || $events instanceof \think\Collection || $events instanceof \think\Paginator): $i = 0; $__LIST__ = $events;if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$event): $mod = ($i % 2 );++$i;?> <tr>
								<td><?php echo htmlentities((string) $event['id']); ?></td>
								<td> <?php switch($event['event_type']): case "device_qq_limit": ?>设备QQ数量超限<?php break; case "ip_qq_limit": ?>IP QQ数量超限<?php break; case "frequent_login": ?>频繁登录<?php break; default: ?><?php echo htmlentities((string) $event['event_type']); ?> <?php endswitch; ?> </td>
								<td>
									<span class="severity-<?php echo htmlentities((string) $event['severity']); ?>"> <?php echo htmlentities((string) $event['severity']); ?> </span>
								</td>
								<td>
									<span class="status-<?php echo htmlentities((string) $event['status']); ?>"> <?php echo htmlentities((string) $event['status']); ?> </span>
								</td>
								<td>
									<small><?php echo htmlentities((string) (isset($event['qq_numbers']) && ($event['qq_numbers'] !== '')?$event['qq_numbers']:'N/A')); ?></small>
								</td>
								<td>
									<small> <?php if($event['device_id']): ?>设备: <?php echo htmlentities((string) $event['device_id']); ?><br><?php endif; if($event['ip_address']): ?>IP: <?php echo htmlentities((string) $event['ip_address']); ?><?php endif; ?> </small>
								</td>
								<td><?php echo htmlentities((string) $event['count']); ?></td>
								<td><small><?php echo htmlentities((string) $event['created_at']); ?></small></td> <?php if($can_manage): ?> <td class="event-actions"> <?php if($event['status'] == 'pending'): ?> <button class="am-btn am-btn-xs am-btn-success" onclick="processEvent(<?php echo htmlentities((string) $event['id']); ?>, 'approve')"> 解封 </button>
									<button class="am-btn am-btn-xs am-btn-danger" onclick="processEvent(<?php echo htmlentities((string) $event['id']); ?>, 'reject')"> 封禁 </button>
									<button class="am-btn am-btn-xs am-btn-secondary" onclick="processEvent(<?php echo htmlentities((string) $event['id']); ?>, 'ignore')"> 忽略 </button> <?php else: ?> <span class="am-text-muted">已处理</span> <?php endif; ?>
								</td> <?php endif; ?>
							</tr> <?php endforeach; endif; else: echo "" ;endif; ?> </tbody>
					</table>
				</div>
				<!-- 分页 -->
				<div class="am-cf pagination-wrapper">
					<div class="am-u-sm-12 am-u-md-6">
						<div class="am-datatable-info">
							显示第 <?php echo htmlentities((string) $events->currentPage()); ?> 页，共 <?php echo htmlentities((string) $events->lastPage()); ?> 页，总计 <?php echo htmlentities((string) $events->total()); ?> 条记录
						</div>
					</div>
					<div class="am-u-sm-12 am-u-md-6">
						<div class="am-datatable-paging am-fr">
							<?php echo htmlentities((string) $events->appends(request()->param())->render()); ?>
						</div>
					</div>
				</div> <?php else: ?> <div class="am-text-center am-padding">
					<i class="am-icon-info-circle am-text-muted"></i>
					<span class="am-text-muted">暂无风险事件</span>
				</div> <?php endif; ?>
			</div>
		</div>
	</div>
</div>


<style>
/* 分页样式优化 */
.pagination-wrapper {
    margin-top: 20px;
    padding: 15px 0;
    border-top: 1px solid #ddd;
}

.am-datatable-info {
    color: #666;
    line-height: 34px;
}

.am-datatable-paging .pagination {
    margin: 0;
    display: inline-block;
}

.pagination li {
    display: inline-block;
    margin: 0 2px;
}

.pagination li a,
.pagination li span {
    display: inline-block;
    padding: 6px 12px;
    line-height: 1.42857143;
    color: #337ab7;
    text-decoration: none;
    background-color: #fff;
    border: 1px solid #ddd;
    border-radius: 4px;
    transition: all 0.2s ease-in-out;
}

.pagination li a:hover {
    color: #23527c;
    background-color: #eee;
    border-color: #ddd;
}

.pagination li.active span {
    color: #fff;
    background-color: #337ab7;
    border-color: #337ab7;
}

.pagination li.disabled span {
    color: #777;
    background-color: #fff;
    border-color: #ddd;
    cursor: not-allowed;
}

/* 筛选栏样式优化 */
.filter-bar {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 6px;
    margin-bottom: 20px;
    border: 1px solid #e9ecef;
}

.filter-bar .am-form-group {
    margin-right: 15px;
    margin-bottom: 10px;
}

.filter-bar label {
    font-weight: 500;
    margin-right: 8px;
    color: #495057;
}

.filter-bar .am-form-field {
    border-radius: 4px;
    border: 1px solid #ced4da;
    padding: 6px 12px;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.filter-bar .am-form-field:focus {
    border-color: #80bdff;
    outline: 0;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* 状态和严重程度标签样式 */
.severity-high {
    color: #dc3545;
    font-weight: bold;
}

.severity-medium {
    color: #fd7e14;
    font-weight: bold;
}

.severity-low {
    color: #28a745;
    font-weight: bold;
}

.status-pending {
    color: #ffc107;
    font-weight: bold;
}

.status-processed {
    color: #28a745;
    font-weight: bold;
}

.status-ignored {
    color: #6c757d;
    font-weight: bold;
}

/* 事件操作按钮样式 */
.event-actions {
    white-space: nowrap;
}

.event-actions .am-btn {
    margin-right: 5px;
    margin-bottom: 2px;
}

/* 表格样式优化 */
.am-table th {
    background-color: #f8f9fa;
    font-weight: 600;
    border-bottom: 2px solid #dee2e6;
}

.am-table td {
    vertical-align: middle;
}

.am-table-hover tbody tr:hover {
    background-color: #f5f5f5;
}

/* 面板样式优化 */
.am-panel-hd {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    font-weight: 600;
    border-radius: 6px 6px 0 0;
}

.am-panel-hd .am-btn {
    border-color: rgba(255, 255, 255, 0.3);
    color: white;
}

.am-panel-hd .am-btn:hover {
    background-color: rgba(255, 255, 255, 0.1);
    border-color: rgba(255, 255, 255, 0.5);
}

/* 响应式优化 */
@media (max-width: 768px) {
    .filter-bar .am-form-group {
        margin-right: 0;
        margin-bottom: 15px;
        width: 100%;
    }

    .filter-bar .am-form-field {
        width: 100%;
    }

    .pagination-wrapper .am-u-sm-12 {
        text-align: center;
        margin-bottom: 10px;
    }

    .am-datatable-paging {
        text-align: center !important;
    }
}
</style>

<script>
	function processEvent(eventId, action) {
	    console.log('processEvent called:', eventId, action);

	    var actionText = {
	        'approve': '解封',
	        'reject': '封禁',
	        'ignore': '忽略'
	    };

	    // 使用简单的confirm对话框进行测试
	    var reason = prompt('请输入处理说明（' + actionText[action] + '）:', '');
	    if (reason === null) {
	        return; // 用户取消
	    }

	    // 直接提交处理
	    $.post('/manage/admin/risk/processEvent', {
	        event_id: eventId,
	        action: action,
	        admin_note: reason
	    }, function(response) {
	        if(response.code == 1) {
	            layer.msg('处理成功', {icon: 1});
	            setTimeout(function() {
	                location.reload();
	            }, 1000);
	        } else {
	            layer.msg('处理失败: ' + response.msg, {icon: 2});
	        }
	    }, 'json').fail(function() {
	        layer.msg('请求失败，请重试', {icon: 2});
	    });
	}



	// 页面加载完成后初始化
	$(document).ready(function() {
	    console.log('风控事件页面已加载');
	});
</script>
	   
	  <div id="outerdiv" style="position:fixed;top:0;left:0;background:rgba(0,0,0,0.7);z-index:2;width:100%;height:100%;display:none;">
		<div id="innerdiv" style="position:absolute;">
		  <img id="bigimg" style="border:5px solid #fff;" src="" />
		</div>
	  </div>
	  <footer class="admin-content-footer">
		<hr>
		<p class="am-padding-left">© 2018 duoluosb.</p>
	  </footer>
	</div>

	<script>
	// 页面初始化脚本

	// 窗口大小改变时隐藏移动侧边栏
	window.addEventListener('resize', function() {
		if (window.innerWidth > 640) {
			hideMobileSidebar();
		}
	});

	// 页面加载完成后的初始化
	document.addEventListener('DOMContentLoaded', function() {
		// 添加触摸事件支持
		var toggleBtn = document.querySelector('.mobile-sidebar-toggle');
		if (toggleBtn) {
			toggleBtn.addEventListener('touchstart', function(e) {
				e.preventDefault();
				toggleMobileSidebar();
			});
		}
	});
	</script>
</body>

<script>
    var _hmt = _hmt || [];
    (function() {
      var hm = document.createElement("script");
      hm.src = "https://hm.baidu.com/hm.js?00e000ae4edf31394d2153c309efbdec";
      var s = document.getElementsByTagName("script")[0]; 
      s.parentNode.insertBefore(hm, s);
    })();
</script>