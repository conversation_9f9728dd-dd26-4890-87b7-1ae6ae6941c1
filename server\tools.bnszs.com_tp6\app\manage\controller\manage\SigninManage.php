<?php

namespace app\manage\controller\manage;

use app\common\controller\BaseController;
use think\App;
use think\Request;
use think\facade\Cache;
use think\facade\Db;
use think\facade\View;
use app\manage\model\UserAdmin as UserAdmin;

class SigninManage extends BaseController
{
    private $admin = 0;
    private $adminInfo = null;
    protected $request;

    public function __construct(App $app, Request $request) {
        parent::__construct($app, $request);
        $this->request = $request;

        $admin = $this->admin = $_SESSION['admin'] ?? null;
        if($admin == 0 || $admin == null) {
            if ($request->isAjax()) {
                $response = json([
                    'code' => 0,
                    'msg' => '请先登录管理员账户',
                    'redirect' => '/manage/login?type=admin&callback=/manage'
                ]);
                $response->send();
                exit;
            } else {
                header("Location:/manage/login?type=admin&callback=/manage");
                die;
            }
        }

        // 获取管理员信息
        $this->adminInfo = UserAdmin::where('uid', $admin)->find();
        if(!$this->adminInfo) {
            if ($request->isAjax()) {
                $response = json([
                    'code' => 0,
                    'msg' => '管理员信息不存在',
                    'redirect' => '/manage/login?type=admin&callback=/manage'
                ]);
                $response->send();
                exit;
            } else {
                header("Location:/manage/login?type=admin&callback=/manage");
                die;
            }
        }

        // 检查签到管理权限
        if(!$this->checkSigninPermission()) {
            abort(403, '您没有签到管理权限');
        }
    }

    /**
     * 检查签到管理权限
     */
    private function checkSigninPermission() {
        // 超级管理员拥有所有权限
        if($this->adminInfo['super'] == 1) {
            return true;
        }

        // 检查是否有签到管理相关权限 (14, 15)
        $powers = explode(',', $this->adminInfo['power']);
        $signinPowers = [14, 15]; // 查看、管理权限
        
        foreach($signinPowers as $power) {
            if(in_array($power, $powers)) {
                return true;
            }
        }
        
        return false;
    }

    /**
     * 检查特定权限
     */
    private function checkPermission($powerID) {
        if($this->adminInfo['super'] == 1) {
            return true;
        }
        
        $powers = explode(',', $this->adminInfo['power']);
        return in_array($powerID, $powers);
    }

    /**
     * 签到管理页面
     */
    public function index() {
        if($this->request->isPost()) {
            // 处理POST请求
            $action = $this->request->param('action', '');
            
            switch($action) {
                case 'clearCache':
                    return $this->clearSigninCache();
                case 'getCacheStats':
                    return $this->getCacheStats();
                case 'testRedis':
                    return $this->testRedisConnection();
                default:
                    return json(['code' => 0, 'msg' => '未知操作']);
            }
        }
        
        try {
            // 获取Redis连接配置
            $redisConfig = config('cache');

            $data = [
                'admin_info' => $this->adminInfo,
                'redis_config' => $redisConfig,
                'can_manage' => $this->checkPermission(15) // 管理权限
            ];

            return View::fetch('/admin/signin_manage', $data);

        } catch (\Exception $e) {
            abort(500, '获取签到管理页面失败: ' . $e->getMessage());
        }
    }

    /**
     * 清除签到缓存
     */
    private function clearSigninCache() {
        if(!$this->checkPermission(15)) {
            return json(['code' => 0, 'msg' => '您没有签到管理权限']);
        }

        try {
            $cacheType = $this->request->param('cache_type', 'all');
            $activityId = $this->request->param('activity_id', '');

            // 检查Redis扩展
            if (!extension_loaded('redis')) {
                return json(['code' => 0, 'msg' => 'Redis扩展未安装，无法执行缓存操作']);
            }

            // 获取Redis连接
            $redis = new \Redis();
            $redisConfig = config('cache');

            // 验证配置
            if (!$redisConfig || !isset($redisConfig['host']) || !isset($redisConfig['port'])) {
                return json(['code' => 0, 'msg' => 'Redis配置不完整']);
            }

            if (!$redis->connect($redisConfig['host'], $redisConfig['port'])) {
                return json(['code' => 0, 'msg' => '无法连接到Redis服务器: ' . $redisConfig['host'] . ':' . $redisConfig['port']]);
            }

            if (!empty($redisConfig['password'])) {
                if (!$redis->auth($redisConfig['password'])) {
                    return json(['code' => 0, 'msg' => 'Redis认证失败']);
                }
            }

            $clearedKeys = [];
            $patterns = [];

            switch ($cacheType) {
                case 'activity_rewards':
                    // 清除活动奖励缓存
                    if ($activityId) {
                        $patterns[] = "activity_rewards_{$activityId}";
                    } else {
                        $patterns[] = "activity_rewards_*";
                    }
                    break;

                case 'user_draw':
                    // 清除用户签到数据缓存
                    if ($activityId) {
                        $patterns[] = "user_draw_*_{$activityId}";
                    } else {
                        $patterns[] = "user_draw_*";
                    }
                    break;

                case 'device_signin':
                    // 清除设备签到状态缓存
                    if ($activityId) {
                        $patterns[] = "device_signin_{$activityId}_*";
                    } else {
                        $patterns[] = "device_signin_*";
                    }
                    break;

                case 'signin_unavailable':
                    // 清除签到不可用状态缓存
                    $patterns[] = "signin_unavailable_*";
                    break;

                case 'signin_time':
                    // 清除用户签到时间缓存（哈希表）
                    if ($activityId) {
                        $patterns[] = "signin_time_{$activityId}";
                    } else {
                        $patterns[] = "signin_time_*";
                    }
                    break;

                case 'all':
                default:
                    // 清除所有签到相关缓存
                    $patterns = [
                        'activity_rewards_*',
                        'user_draw_*',
                        'device_signin_*',
                        'signin_unavailable_*',
                        'signin_time_*'  // 新增：用户签到时间哈希表缓存
                    ];
                    break;
            }

            // 执行缓存清理
            foreach ($patterns as $pattern) {
                $keys = $redis->keys($pattern);
                if (is_array($keys) && !empty($keys)) {
                    foreach ($keys as $key) {
                        if ($redis->del($key)) {
                            $clearedKeys[] = $key;
                        }
                    }
                }
            }

            $redis->close();

            // 记录管理员操作日志
            $this->logAdminAction('clear_signin_cache', [
                'cache_type' => $cacheType,
                'activity_id' => $activityId,
                'cleared_keys_count' => count($clearedKeys),
                'patterns' => $patterns
            ]);

            return json(['code' => 1, 'msg' => '缓存清理成功，共清理了 ' . count($clearedKeys) . ' 个缓存键']);

        } catch (\Exception $e) {
            return json(['code' => 0, 'msg' => '缓存清理失败: ' . $e->getMessage()]);
        }
    }

    /**
     * 测试Redis连接（私有方法）
     */
    private function testRedisConnection() {
        try {
            $redis = Cache::store('redis')->handler();

            // 测试基本连接
            $redis->ping();

            // 测试读写
            $testKey = 'test_connection_' . time();
            $redis->set($testKey, 'test_value', 10);
            $value = $redis->get($testKey);
            $redis->del($testKey);

            if ($value === 'test_value') {
                return json([
                    'code' => 1,
                    'msg' => 'Redis连接正常，读写测试通过'
                ]);
            } else {
                return json([
                    'code' => 0,
                    'msg' => 'Redis连接异常，读写测试失败'
                ]);
            }

        } catch (\Exception $e) {
            return json([
                'code' => 0,
                'msg' => 'Redis连接失败: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * 获取缓存统计信息
     */
    private function getCacheStats() {
        if(!$this->checkPermission(14)) {
            return json(['code' => 0, 'msg' => '您没有查看权限']);
        }

        try {
            // 检查Redis扩展
            if (!extension_loaded('redis')) {
                return json(['code' => 0, 'msg' => 'Redis扩展未安装，无法获取缓存统计']);
            }

            // 获取Redis连接
            $redis = new \Redis();
            $redisConfig = config('cache');

            // 验证配置
            if (!$redisConfig || !isset($redisConfig['host']) || !isset($redisConfig['port'])) {
                return json(['code' => 0, 'msg' => 'Redis配置不完整']);
            }

            if (!$redis->connect($redisConfig['host'], $redisConfig['port'])) {
                return json(['code' => 0, 'msg' => '无法连接到Redis服务器: ' . $redisConfig['host'] . ':' . $redisConfig['port']]);
            }

            if (!empty($redisConfig['password'])) {
                if (!$redis->auth($redisConfig['password'])) {
                    return json(['code' => 0, 'msg' => 'Redis认证失败']);
                }
            }

            // 选择正确的数据库
            if (isset($redisConfig['select'])) {
                $redis->select($redisConfig['select']);
            }

            // 获取缓存统计
            $activityRewardsKeys = $redis->keys('activity_rewards_*');
            $userDrawKeys = $redis->keys('user_draw_*');
            $deviceSigninKeys = $redis->keys('device_signin_*');
            $signinUnavailableKeys = $redis->keys('signin_unavailable_*');

            // 获取内存信息
            $memoryInfo = $redis->info('memory');
            $totalMemory = 'N/A';
            if (is_array($memoryInfo) && isset($memoryInfo['used_memory_human'])) {
                $totalMemory = $memoryInfo['used_memory_human'];
            }

            $stats = [
                'activity_rewards' => is_array($activityRewardsKeys) ? count($activityRewardsKeys) : 0,
                'user_draw' => is_array($userDrawKeys) ? count($userDrawKeys) : 0,
                'device_signin' => is_array($deviceSigninKeys) ? count($deviceSigninKeys) : 0,
                'signin_unavailable' => is_array($signinUnavailableKeys) ? count($signinUnavailableKeys) : 0,
                'total_memory' => $totalMemory
            ];

            $redis->close();

            return json(['code' => 1, 'msg' => '获取成功', 'data' => $stats]);

        } catch (\Exception $e) {
            return json(['code' => 0, 'msg' => '获取缓存统计失败: ' . $e->getMessage()]);
        }
    }

    /**
     * 记录管理员操作日志
     */
    private function logAdminAction($action, $data) {
        $logData = [
            'admin_uid' => $this->admin,
            'admin_username' => $this->adminInfo['username'],
            'action' => $action,
            'data' => json_encode($data, JSON_UNESCAPED_UNICODE),
            'ip' => $this->request->ip(),
            'created_at' => date('Y-m-d H:i:s')
        ];

        try {
            // 记录到数据库（与Go系统共享）
            Db::name('bns_useradminlog')->insert($logData);
        } catch (\Exception $e) {
            // 如果数据库记录失败，记录到文件
            $logFile = \think\facade\App::getRuntimePath() . 'log/admin_signin_manage.log';
            $logContent = "[" . date('Y-m-d H:i:s') . "] " . json_encode($logData, JSON_UNESCAPED_UNICODE) . "\n";
            file_put_contents($logFile, $logContent, FILE_APPEND | LOCK_EX);
        }
    }
}
