{layout name="manage/template" /}

<div class="admin-content">
	<div class="admin-content-body">
		<div class="am-cf am-padding am-padding-bottom-0">
			<div class="am-fl am-cf">
				<strong class="am-text-primary am-text-lg">风险事件管理</strong> / <small>Risk Events Management</small>
			</div>
		</div>
		<hr>
		<!-- 筛选栏 -->
		<div class="filter-bar">
			<form class="am-form am-form-inline" method="get">
				<div class="am-form-group">
					<label>状态:</label>
					<select name="status" class="am-form-field">
						<option value="">全部</option>
						<option value="pending" {if condition="$status == 'pending'" }selected{/if}>待处理</option>
						<option value="processed" {if condition="$status == 'processed'" }selected{/if}>已处理</option>
						<option value="ignored" {if condition="$status == 'ignored'" }selected{/if}>已忽略</option>
					</select>
				</div>
				<div class="am-form-group">
					<label>严重程度:</label>
					<select name="severity" class="am-form-field">
						<option value="">全部</option>
						<option value="high" {if condition="$severity == 'high'" }selected{/if}>高风险</option>
						<option value="medium" {if condition="$severity == 'medium'" }selected{/if}>中风险</option>
						<option value="low" {if condition="$severity == 'low'" }selected{/if}>低风险</option>
					</select>
				</div>
				<button type="submit" class="am-btn am-btn-primary">筛选</button>
				<a href="/manage/admin/risk/events" class="am-btn am-btn-default">重置</a>
			</form>
		</div>
		<!-- 事件列表 -->
		<div class="am-panel am-panel-default">
			<div class="am-panel-hd"> 风险事件列表 <div class="am-fr">
					<a href="/manage/admin/risk/dashboard" class="am-btn am-btn-xs am-btn-secondary">
						<i class="am-icon-dashboard"></i> 返回仪表板 </a>
				</div>
			</div>
			<div class="am-panel-bd"> {if condition="$events->items()"} <div class="am-scrollable-horizontal">
					<table class="am-table am-table-striped am-table-hover">
						<thead>
							<tr>
								<th>ID</th>
								<th>事件类型</th>
								<th>严重程度</th>
								<th>状态</th>
								<th>涉及QQ号</th>
								<th>设备/IP</th>
								<th>触发次数</th>
								<th>创建时间</th> {if condition="$can_manage"} <th>操作</th> {/if}
							</tr>
						</thead>
						<tbody> {volist name="events" id="event"} <tr>
								<td>{$event.id}</td>
								<td> {switch name="event.event_type"} {case value="device_qq_limit"}设备QQ数量超限{/case} {case value="ip_qq_limit"}IP QQ数量超限{/case} {case value="frequent_login"}频繁登录{/case} {default /}{$event.event_type} {/switch} </td>
								<td>
									<span class="severity-{$event.severity}"> {$event.severity} </span>
								</td>
								<td>
									<span class="status-{$event.status}"> {$event.status} </span>
								</td>
								<td>
									<small>{$event.qq_numbers|default='N/A'}</small>
								</td>
								<td>
									<small> {if condition="$event.device_id"}设备: {$event.device_id}<br>{/if} {if condition="$event.ip_address"}IP: {$event.ip_address}{/if} </small>
								</td>
								<td>{$event.count}</td>
								<td><small>{$event.created_at}</small></td> {if condition="$can_manage"} <td class="event-actions"> {if condition="$event.status == 'pending'"} <button class="am-btn am-btn-xs am-btn-success" onclick="processEvent({$event.id}, 'approve')"> 解封 </button>
									<button class="am-btn am-btn-xs am-btn-danger" onclick="processEvent({$event.id}, 'reject')"> 封禁 </button>
									<button class="am-btn am-btn-xs am-btn-secondary" onclick="processEvent({$event.id}, 'ignore')"> 忽略 </button> {else/} <span class="am-text-muted">已处理</span> {/if}
								</td> {/if}
							</tr> {/volist} </tbody>
					</table>
				</div>
				<!-- 分页 -->
				<div class="am-cf pagination-wrapper">
					<div class="am-u-sm-12 am-u-md-6">
						<div class="am-datatable-info">
							显示第 {$events->currentPage()} 页，共 {$events->lastPage()} 页，总计 {$events->total()} 条记录
						</div>
					</div>
					<div class="am-u-sm-12 am-u-md-6">
						<div class="am-datatable-paging am-fr">
							{$events->appends(request()->param())->render()}
						</div>
					</div>
				</div> {else/} <div class="am-text-center am-padding">
					<i class="am-icon-info-circle am-text-muted"></i>
					<span class="am-text-muted">暂无风险事件</span>
				</div> {/if}
			</div>
		</div>
	</div>
</div>


<style>
/* 分页样式优化 */
.pagination-wrapper {
    margin-top: 20px;
    padding: 15px 0;
    border-top: 1px solid #ddd;
}

.am-datatable-info {
    color: #666;
    line-height: 34px;
}

.am-datatable-paging .pagination {
    margin: 0;
    display: inline-block;
}

.pagination li {
    display: inline-block;
    margin: 0 2px;
}

.pagination li a,
.pagination li span {
    display: inline-block;
    padding: 6px 12px;
    line-height: 1.42857143;
    color: #337ab7;
    text-decoration: none;
    background-color: #fff;
    border: 1px solid #ddd;
    border-radius: 4px;
    transition: all 0.2s ease-in-out;
}

.pagination li a:hover {
    color: #23527c;
    background-color: #eee;
    border-color: #ddd;
}

.pagination li.active span {
    color: #fff;
    background-color: #337ab7;
    border-color: #337ab7;
}

.pagination li.disabled span {
    color: #777;
    background-color: #fff;
    border-color: #ddd;
    cursor: not-allowed;
}

/* 筛选栏样式优化 */
.filter-bar {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 6px;
    margin-bottom: 20px;
    border: 1px solid #e9ecef;
}

.filter-bar .am-form-group {
    margin-right: 15px;
    margin-bottom: 10px;
}

.filter-bar label {
    font-weight: 500;
    margin-right: 8px;
    color: #495057;
}

.filter-bar .am-form-field {
    border-radius: 4px;
    border: 1px solid #ced4da;
    padding: 6px 12px;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.filter-bar .am-form-field:focus {
    border-color: #80bdff;
    outline: 0;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* 状态和严重程度标签样式 */
.severity-high {
    color: #dc3545;
    font-weight: bold;
}

.severity-medium {
    color: #fd7e14;
    font-weight: bold;
}

.severity-low {
    color: #28a745;
    font-weight: bold;
}

.status-pending {
    color: #ffc107;
    font-weight: bold;
}

.status-processed {
    color: #28a745;
    font-weight: bold;
}

.status-ignored {
    color: #6c757d;
    font-weight: bold;
}

/* 事件操作按钮样式 */
.event-actions {
    white-space: nowrap;
}

.event-actions .am-btn {
    margin-right: 5px;
    margin-bottom: 2px;
}

/* 表格样式优化 */
.am-table th {
    background-color: #f8f9fa;
    font-weight: 600;
    border-bottom: 2px solid #dee2e6;
}

.am-table td {
    vertical-align: middle;
}

.am-table-hover tbody tr:hover {
    background-color: #f5f5f5;
}

/* 面板样式优化 */
.am-panel-hd {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    font-weight: 600;
    border-radius: 6px 6px 0 0;
}

.am-panel-hd .am-btn {
    border-color: rgba(255, 255, 255, 0.3);
    color: white;
}

.am-panel-hd .am-btn:hover {
    background-color: rgba(255, 255, 255, 0.1);
    border-color: rgba(255, 255, 255, 0.5);
}

/* 响应式优化 */
@media (max-width: 768px) {
    .filter-bar .am-form-group {
        margin-right: 0;
        margin-bottom: 15px;
        width: 100%;
    }

    .filter-bar .am-form-field {
        width: 100%;
    }

    .pagination-wrapper .am-u-sm-12 {
        text-align: center;
        margin-bottom: 10px;
    }

    .am-datatable-paging {
        text-align: center !important;
    }
}
</style>

<script>
	function processEvent(eventId, action) {
	    console.log('processEvent called:', eventId, action);

	    var actionText = {
	        'approve': '解封',
	        'reject': '封禁',
	        'ignore': '忽略'
	    };

	    // 使用简单的confirm对话框进行测试
	    var reason = prompt('请输入处理说明（' + actionText[action] + '）:', '');
	    if (reason === null) {
	        return; // 用户取消
	    }

	    // 直接提交处理
	    $.post('/manage/admin/risk/processEvent', {
	        event_id: eventId,
	        action: action,
	        admin_note: reason
	    }, function(response) {
	        if(response.code == 1) {
	            layer.msg('处理成功', {icon: 1});
	            setTimeout(function() {
	                location.reload();
	            }, 1000);
	        } else {
	            layer.msg('处理失败: ' + response.msg, {icon: 2});
	        }
	    }, 'json').fail(function() {
	        layer.msg('请求失败，请重试', {icon: 2});
	    });
	}



	// 页面加载完成后初始化
	$(document).ready(function() {
	    console.log('风控事件页面已加载');
	});
</script>