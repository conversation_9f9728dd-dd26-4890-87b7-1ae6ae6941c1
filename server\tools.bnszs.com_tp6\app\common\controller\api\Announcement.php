<?php

namespace app\common\controller\api;

use app\common\controller\ApiBase;
use app\manage\model\Announcement as AnnouncementModel;
use think\Request;

/**
 * 公告API控制器
 */
class Announcement extends ApiBase
{
    /**
     * 获取公告列表
     */
    public function list()
    {
        try {
            $clientType = $this->request->param('client_type', 'all');
            $version = $this->request->param('version', '');
            $limit = $this->request->param('limit', 20);

            $announcements = AnnouncementModel::getClientList($clientType, $version);
            
            // 限制返回数量
            if ($limit > 0 && count($announcements) > $limit) {
                $announcements = array_slice($announcements->toArray(), 0, $limit);
            }

            return $this->success('获取成功', [
                'announcements' => $announcements,
                'count' => count($announcements),
                'version' => AnnouncementModel::getCurrentVersion()
            ]);

        } catch (\Exception $e) {
            return $this->error('获取公告列表失败: ' . $e->getMessage());
        }
    }

    /**
     * 检查公告更新
     */
    public function checkUpdate()
    {
        try {
            $clientVersion = $this->request->param('version', 0);
            $currentVersion = AnnouncementModel::getCurrentVersion();

            $hasUpdate = $currentVersion > $clientVersion;

            return $this->success('检查完成', [
                'has_update' => $hasUpdate,
                'current_version' => $currentVersion,
                'client_version' => $clientVersion
            ]);

        } catch (\Exception $e) {
            return $this->error('检查更新失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取公告详情
     */
    public function detail()
    {
        try {
            $id = $this->request->param('id');
            
            if (!$id) {
                return $this->error('参数错误');
            }

            $announcement = AnnouncementModel::find($id);
            if (!$announcement) {
                return $this->error('公告不存在');
            }

            // 检查公告是否已发布且在有效期内
            if ($announcement->status !== AnnouncementModel::STATUS_PUBLISHED) {
                return $this->error('公告未发布');
            }

            // 检查时间范围
            $now = date('Y-m-d H:i:s');
            if ($announcement->start_time && $announcement->start_time > $now) {
                return $this->error('公告未到显示时间');
            }
            if ($announcement->end_time && $announcement->end_time < $now) {
                return $this->error('公告已过期');
            }

            // 增加查看次数
            $announcement->incrementViewCount();

            return $this->success('获取成功', [
                'announcement' => $announcement->toArray()
            ]);

        } catch (\Exception $e) {
            return $this->error('获取公告详情失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取公告统计信息
     */
    public function stats()
    {
        try {
            $stats = AnnouncementModel::getStats();
            $currentVersion = AnnouncementModel::getCurrentVersion();

            return $this->success('获取成功', [
                'stats' => $stats,
                'version' => $currentVersion,
                'timestamp' => time()
            ]);

        } catch (\Exception $e) {
            return $this->error('获取统计信息失败: ' . $e->getMessage());
        }
    }

    /**
     * 健康检查
     */
    public function health()
    {
        try {
            // 检查数据库连接
            $count = AnnouncementModel::count();
            $version = AnnouncementModel::getCurrentVersion();

            return $this->success('服务正常', [
                'status' => 'healthy',
                'announcement_count' => $count,
                'current_version' => $version,
                'timestamp' => time()
            ]);

        } catch (\Exception $e) {
            return $this->error('服务异常: ' . $e->getMessage(), [], 500);
        }
    }
}
