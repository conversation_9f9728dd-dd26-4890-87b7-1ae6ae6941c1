<?php

namespace app\common\model;

use think\Model;
use think\facade\Db;

/**
 * 公告模型
 */
class Announcement extends Model
{
    protected $table = 'bns_announcement';
    protected $pk = 'id';

    // 设置字段信息
    protected $schema = [
        'id'                  => 'int',
        'title'               => 'string',
        'content'             => 'text',
        'type'                => 'int',
        'status'              => 'int',
        'priority'            => 'int',
        'start_time'          => 'datetime',
        'end_time'            => 'datetime',
        'target_client'       => 'string',
        'version_requirement' => 'string',
        'view_count'          => 'int',
        'admin_uid'           => 'int',
        'admin_username'      => 'string',
        'created_at'          => 'datetime',
        'updated_at'          => 'datetime',
    ];

    // 自动时间戳
    protected $autoWriteTimestamp = true;
    protected $createTime = 'created_at';
    protected $updateTime = 'updated_at';

    // 类型常量
    const TYPE_NORMAL = 1;    // 普通公告
    const TYPE_IMPORTANT = 2; // 重要公告
    const TYPE_URGENT = 3;    // 紧急公告

    // 状态常量
    const STATUS_DRAFT = 0;    // 草稿
    const STATUS_PUBLISHED = 1; // 已发布
    const STATUS_OFFLINE = 2;   // 已下线

    /**
     * 获取管理员列表（分页）
     */
    public static function getAdminList($page = 1, $pageSize = 20, $filters = [])
    {
        $query = self::order('priority desc, created_at desc');

        // 应用筛选条件
        if (!empty($filters['status']) && $filters['status'] !== '') {
            $query->where('status', $filters['status']);
        }

        if (!empty($filters['type']) && $filters['type'] !== '') {
            $query->where('type', $filters['type']);
        }

        if (!empty($filters['keyword'])) {
            $query->where('title', 'like', '%' . $filters['keyword'] . '%');
        }

        return $query->paginate($pageSize, false, ['page' => $page]);
    }

    /**
     * 获取统计信息
     */
    public static function getStats()
    {
        return [
            'total' => self::count(),
            'published' => self::where('status', self::STATUS_PUBLISHED)->count(),
            'draft' => self::where('status', self::STATUS_DRAFT)->count(),
            'offline' => self::where('status', self::STATUS_OFFLINE)->count(),
        ];
    }

    /**
     * 获取客户端公告列表
     */
    public static function getClientList($clientType = 'all', $version = null)
    {
        $query = self::where('status', self::STATUS_PUBLISHED)
                    ->where(function($query) {
                        $query->whereNull('start_time')
                              ->whereOr('start_time', '<=', date('Y-m-d H:i:s'));
                    })
                    ->where(function($query) {
                        $query->whereNull('end_time')
                              ->whereOr('end_time', '>=', date('Y-m-d H:i:s'));
                    });

        // 客户端类型筛选
        if ($clientType !== 'all') {
            $query->where(function($query) use ($clientType) {
                $query->where('target_client', 'all')
                      ->whereOr('target_client', $clientType);
            });
        }

        // 版本要求筛选（简单实现，实际可能需要更复杂的版本比较）
        if ($version) {
            $query->where(function($query) use ($version) {
                $query->whereNull('version_requirement')
                      ->whereOr('version_requirement', '')
                      ->whereOr('version_requirement', '<=', $version);
            });
        }

        return $query->order('priority desc, created_at desc')->select();
    }

    /**
     * 创建公告
     */
    public static function createAnnouncement($data, $admin)
    {
        $announcement = new self();
        $announcement->title = $data['title'];
        $announcement->content = $data['content'];
        $announcement->type = $data['type'] ?? self::TYPE_NORMAL;
        $announcement->status = $data['status'] ?? self::STATUS_DRAFT;
        $announcement->priority = $data['priority'] ?? 0;
        $announcement->start_time = !empty($data['start_time']) ? $data['start_time'] : null;
        $announcement->end_time = !empty($data['end_time']) ? $data['end_time'] : null;
        $announcement->target_client = $data['target_client'] ?? 'all';
        $announcement->version_requirement = $data['version_requirement'] ?? null;
        $announcement->admin_uid = $admin['uid'];
        $announcement->admin_username = $admin['username'];
        $announcement->view_count = 0;

        if ($announcement->save()) {
            // 更新版本号
            self::updateVersion();
            return $announcement;
        }

        return false;
    }

    /**
     * 更新公告
     */
    public function updateAnnouncement($data, $admin)
    {
        $this->title = $data['title'];
        $this->content = $data['content'];
        $this->type = $data['type'] ?? self::TYPE_NORMAL;
        $this->status = $data['status'] ?? self::STATUS_DRAFT;
        $this->priority = $data['priority'] ?? 0;
        $this->start_time = !empty($data['start_time']) ? $data['start_time'] : null;
        $this->end_time = !empty($data['end_time']) ? $data['end_time'] : null;
        $this->target_client = $data['target_client'] ?? 'all';
        $this->version_requirement = $data['version_requirement'] ?? null;

        if ($this->save()) {
            // 更新版本号
            self::updateVersion();
            return $this;
        }

        return false;
    }

    /**
     * 发布公告
     */
    public function publish()
    {
        $this->status = self::STATUS_PUBLISHED;
        if ($this->save()) {
            self::updateVersion();
            return true;
        }
        return false;
    }

    /**
     * 下线公告
     */
    public function offline()
    {
        $this->status = self::STATUS_OFFLINE;
        if ($this->save()) {
            self::updateVersion();
            return true;
        }
        return false;
    }

    /**
     * 删除公告
     */
    public function deleteAnnouncement()
    {
        if ($this->delete()) {
            self::updateVersion();
            return true;
        }
        return false;
    }

    /**
     * 增加查看次数
     */
    public function incrementViewCount()
    {
        $this->view_count = ($this->view_count ?? 0) + 1;
        return $this->save();
    }

    /**
     * 获取类型文本
     */
    public function getTypeTextAttr($value, $data)
    {
        $types = [
            self::TYPE_NORMAL => '普通',
            self::TYPE_IMPORTANT => '重要',
            self::TYPE_URGENT => '紧急',
        ];
        return $types[$data['type']] ?? '未知';
    }

    /**
     * 获取状态文本
     */
    public function getStatusTextAttr($value, $data)
    {
        $statuses = [
            self::STATUS_DRAFT => '草稿',
            self::STATUS_PUBLISHED => '已发布',
            self::STATUS_OFFLINE => '已下线',
        ];
        return $statuses[$data['status']] ?? '未知';
    }

    /**
     * 获取目标客户端文本
     */
    public function getTargetClientTextAttr($value, $data)
    {
        $clients = [
            'all' => '全部',
            'desktop' => '桌面端',
            'mobile' => '移动端',
        ];
        return $clients[$data['target_client']] ?? '未知';
    }

    /**
     * 更新公告版本号
     */
    private static function updateVersion()
    {
        try {
            Db::name('bns_announcement_version')
              ->where('id', 1)
              ->inc('version')
              ->update(['last_modified' => date('Y-m-d H:i:s')]);
        } catch (\Exception $e) {
            // 如果版本表不存在，忽略错误
            trace('Failed to update announcement version: ' . $e->getMessage(), 'warning');
        }
    }

    /**
     * 获取当前版本号
     */
    public static function getCurrentVersion()
    {
        try {
            $version = Db::name('bns_announcement_version')->where('id', 1)->value('version');
            return $version ?: 1;
        } catch (\Exception $e) {
            return 1;
        }
    }
}
